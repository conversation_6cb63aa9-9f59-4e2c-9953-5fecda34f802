package com.polarion.synchronizer.proxy.feishu.translators;

import com.google.inject.assistedinject.Assisted;
import com.polarion.synchronizer.ILogger;
import com.polarion.synchronizer.mapping.TranslationResult;
import com.polarion.synchronizer.mapping.ValueMapping;
import com.polarion.synchronizer.model.Side;
import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.inject.Inject;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 飞书选项值到Polarion选项值的转换器
 * 支持通过名称进行映射，而不仅仅是ID
 */
public class FeishuToPolarionOptionTranslator extends TypesafeTranslator<String, String, String> {
    
    @NotNull
    private final ILogger logger;
    
    @NotNull
    private final Collection<ValueMapping> valueMappings;
    
    @NotNull
    private final Side fromSide;
    
    // 缓存选项名称到ID的映射
    private final Map<String, String> nameToIdCache = new ConcurrentHashMap<>();
    private final Map<String, String> idToNameCache = new ConcurrentHashMap<>();

    @Inject
    public FeishuToPolarionOptionTranslator(@Assisted Collection<ValueMapping> valueMappings, 
                                           @Assisted Side fromSide,
                                           @NotNull ILogger logger) {
        super(String.class, String.class);
        this.valueMappings = valueMappings;
        this.fromSide = fromSide;
        this.logger = logger;
    }

    @Override
    public TranslationResult<String> translateUnidirectionalTypesafe(@Nullable String sourceValue, 
                                                                   @Nullable String targetValue) {
        String mappedValue = convertOption(sourceValue);
        return createUnidirectionalResult(mappedValue, targetValue);
    }

    @Override
    public TranslationResult<String> translateBidirectionalTypesafe(@Nullable String sourceBaseline, 
                                                                  @Nullable String sourceValue, 
                                                                  @Nullable String targetBaseline, 
                                                                  @Nullable String targetValue) {
        String mappedValue = convertOption(sourceValue);
        return createBidirectionalResult(sourceBaseline, sourceValue, mappedValue, targetBaseline, targetValue);
    }

    /**
     * 转换选项值
     */
    @Nullable
    private String convertOption(@Nullable String optionValue) {
        if (optionValue == null || optionValue.trim().isEmpty()) {
            return null;
        }

        try {
            // 首先检查是否有直接的值映射配置
            String mappedValue = applyValueMappings(optionValue);
            if (mappedValue != null && !mappedValue.equals(optionValue)) {
                logger.debug("通过值映射转换选项: " + optionValue + " -> " + mappedValue);
                return mappedValue;
            }

            // 如果没有直接映射，尝试通过名称查找
            if (fromSide == Side.RIGHT) {
                // 飞书 -> Polarion：尝试将飞书选项ID转换为Polarion选项名称
                return convertFeishuToPolarionOption(optionValue);
            } else {
                // Polarion -> 飞书：尝试将Polarion选项名称转换为飞书选项ID
                return convertPolarionToFeishuOption(optionValue);
            }
            
        } catch (Exception e) {
            logger.warn("选项值转换失败: " + optionValue, e);
            return optionValue; // 转换失败时返回原值
        }
    }

    /**
     * 应用值映射配置
     */
    @Nullable
    private String applyValueMappings(@NotNull String value) {
        for (ValueMapping mapping : valueMappings) {
            if (fromSide == Side.RIGHT && value.equals(mapping.getRight())) {
                return mapping.getLeft();
            } else if (fromSide == Side.LEFT && value.equals(mapping.getLeft())) {
                return mapping.getRight();
            }
        }
        return null;
    }

    /**
     * 将飞书选项ID转换为Polarion选项值
     */
    @Nullable
    private String convertFeishuToPolarionOption(@NotNull String feishuOptionId) {
        try {
            // 首先检查缓存
            String cachedName = idToNameCache.get(feishuOptionId);
            if (cachedName != null) {
                logger.debug("从缓存获取Polarion选项名称: " + feishuOptionId + " -> " + cachedName);
                return cachedName;
            }

            // TODO: 这里可以实现具体的选项查找逻辑
            // 例如：通过飞书API查找选项ID对应的名称
            
            logger.debug("飞书选项转换为Polarion选项: " + feishuOptionId);
            return feishuOptionId; // 暂时返回原值
            
        } catch (Exception e) {
            logger.warn("飞书选项转换失败: " + feishuOptionId, e);
            return feishuOptionId;
        }
    }

    /**
     * 将Polarion选项值转换为飞书选项ID
     */
    @Nullable
    private String convertPolarionToFeishuOption(@NotNull String polarionOption) {
        try {
            // 首先检查缓存
            String cachedId = nameToIdCache.get(polarionOption);
            if (cachedId != null) {
                logger.debug("从缓存获取飞书选项ID: " + polarionOption + " -> " + cachedId);
                return cachedId;
            }

            // TODO: 这里可以实现具体的选项查找逻辑
            // 例如：通过飞书API查找选项名称对应的ID
            
            logger.debug("Polarion选项转换为飞书选项: " + polarionOption);
            return polarionOption; // 暂时返回原值
            
        } catch (Exception e) {
            logger.warn("Polarion选项转换失败: " + polarionOption, e);
            return polarionOption;
        }
    }

    /**
     * 更新选项名称到ID的缓存
     */
    public void updateOptionCache(@NotNull String optionName, @NotNull String optionId) {
        nameToIdCache.put(optionName, optionId);
        idToNameCache.put(optionId, optionName);
        logger.debug("更新选项缓存: " + optionName + " <-> " + optionId);
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        nameToIdCache.clear();
        idToNameCache.clear();
        logger.debug("清空选项缓存");
    }
}
