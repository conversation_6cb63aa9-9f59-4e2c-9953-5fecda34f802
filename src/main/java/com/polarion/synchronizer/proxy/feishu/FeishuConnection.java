package com.polarion.synchronizer.proxy.feishu;

import java.util.ArrayList;
import java.util.Collection;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.lark.project.Client;
import com.polarion.synchronizer.proxy.feishu.auth.AuthValidationResult;
import com.polarion.synchronizer.proxy.feishu.auth.AuthValidator;
import com.polarion.synchronizer.spi.Connection;

/**
 * 飞书项目连接器配置类
 * 定义连接到飞书项目所需的基本参数
 */
public class FeishuConnection extends Connection {

    private String serverUrl;
    private String authMode = "plugin_access_token"; // 访问凭证类型：plugin_access_token, virtual_plugin_token
    private String userKey; // 用户Key，用于指定接口调用的用户标识（仅在 plugin_access_token 模式下使用）

    // 缓存的Client实例，实现单例模式
    private volatile Client cachedClient;
    private final Object clientLock = new Object();

    //用于 xml 配置回显赋值
    public FeishuConnection() {
        // 默认设置为插件访问凭证模式
        this.authMode = "plugin_access_token";
    }
    
    /**
     * 构造函数
     * @param id 连接ID
     * @param serverUrl 服务器地址
     * @param authMode 访问凭证类型
     * @param pluginId App ID
     * @param secret App Secret
     */
    public FeishuConnection(String id, String serverUrl, String authMode, String pluginId, String secret) {
        super(id, pluginId, secret);
        this.serverUrl = serverUrl != null ? serverUrl : "https://project.feishu.cn";
        this.authMode = authMode != null ? authMode : "plugin_access_token";
    }
    
    public String getServerUrl() {
        return serverUrl;
    }
    
    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }
    
    public String getAuthMode() {
        return authMode != null ? authMode : "plugin_access_token";
    }

    public void setAuthMode(String authMode) {
        this.authMode = authMode;
    }

    /**
     * 获取 Plugin ID (App ID)
     * 映射到父类的 user 字段
     */
    public String getPluginId() {
        return this.getUser();
    }

    /**
     * 设置 Plugin ID (App ID)
     * 映射到父类的 user 字段
     */
    public void setPluginId(String pluginId) {
        this.setUser(pluginId);
    }

    /**
     * 获取 Plugin Secret (App Secret)
     * 映射到父类的 password 字段，从保险库中读取
     */
    public String getPluginSecret() {
        return this.getPassword();
    }

    /**
     * 设置 Plugin Secret (App Secret)
     * 映射到父类的 password 字段，存储到保险库中
     */
    public void setPluginSecret(String pluginSecret) {
        if (pluginSecret != null && !pluginSecret.isEmpty()) {
            this.setPassword(pluginSecret);
        }
    }

    /**
     * 获取用户Key
     * 用于指定接口调用的用户标识（仅在 plugin_access_token 模式下使用）
     */
    public String getUserKey() {
        return userKey;
    }

    /**
     * 设置用户Key
     * 用于指定接口调用的用户标识（仅在 plugin_access_token 模式下使用）
     */
    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }
    
    /**
     * 检查连接配置的有效性
     * @return 错误信息，如果配置有效则返回null
     */
    @Nullable
    public String check() {
        Collection<String> missing = new ArrayList<>();

        if (getServerUrl() == null || getServerUrl().isEmpty()) {
            missing.add("服务器地址");
        }

        String authMode = getAuthMode();
        switch (authMode) {
            case "plugin_access_token":
            case "virtual_plugin_token":
                if (getPluginId() == null || getPluginId().isEmpty()) {
                    missing.add("App ID");
                }
                if (getPluginSecret() == null || getPluginSecret().isEmpty()) {
                    missing.add("App Secret");
                }
                break;
            default:
                return "不支持的访问凭证类型: " + authMode;
        }

        if (!missing.isEmpty()) {
            return "缺少必需的配置项: " + String.join(", ", missing);
        }

        // 验证服务器地址格式
        if (!getServerUrl().startsWith("http://") && !getServerUrl().startsWith("https://")) {
            return "服务器地址必须以 http:// 或 https:// 开头";
        }
        if (!testConnection()) {
			return "身份信息验证失败";
		}
        return null;
    }
    
    /**
     * 获取完整的API基础URL
     * @return API基础URL
     */
    @NotNull
    public String getApiBaseUrl() {
        String baseUrl = getServerUrl();
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        return baseUrl + "/open_api";
    }
    
    /**
     * 测试连接是否可用
     * @return 连接测试结果
     */
    public boolean testConnection() {
		AuthValidationResult validatePluginCredentials = AuthValidator.validatePluginCredentials(getPluginId(),
				getPluginSecret(), getUserKey(), getServerUrl());;
		return validatePluginCredentials.isSuccess();
    }

    /**
     * 获取飞书API客户端实例（单例模式）
     * @return 飞书客户端实例，如果配置无效则返回null
     */
    @Nullable
    public Client getFeishuClient() {
        if (cachedClient == null) {
            synchronized (clientLock) {
                if (cachedClient == null) {
                    cachedClient = createFeishuClient();
                }
            }
        }
        return cachedClient;
    }

    /**
     * 创建飞书API客户端实例
     * @return 飞书客户端实例，如果配置无效则返回null
     */
    @Nullable
    private Client createFeishuClient() {
        try {
            String pluginId = getPluginId();
            String pluginSecret = getPluginSecret();
            String serverUrl = getServerUrl();

            if (pluginId == null || pluginSecret == null || serverUrl == null) {
                return null;
            }

            return Client.newBuilder(pluginId, pluginSecret)
                .openBaseUrl(serverUrl)
                .requestTimeout(30000)
                .logReqAtDebug(true)
                .build();

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 清除缓存的Client实例
     * 当连接配置发生变化时调用
     */
    public void clearClientCache() {
        synchronized (clientLock) {
            cachedClient = null;
        }
    }
}
