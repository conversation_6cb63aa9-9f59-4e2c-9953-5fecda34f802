/**
 * 飞书项目连接器UI组件
 * 定义前端配置界面的JavaScript组件
 */

// 飞书项目代理配置模型
App.FeishuProxyConfiguration = App.ProxyConfiguration.extend({
    resourceProperties: [
        'proxyType',
        'projectKey',
        'workItemTypes',
        'excludeChartType',
        'connection'
    ],
    type: "",
    description: function() {
        var connection = this.get('connection');
        var projectKey = this.get('projectKey');

        if (!connection) {
            return '未配置连接';
        }

        var serverUrl = connection.get ? connection.get('serverUrl') : connection.serverUrl;
        if (!serverUrl) {
            serverUrl = 'https://project.feishu.cn';
        }

        if (!projectKey) {
            return '未指定项目Key，服务器: ' + serverUrl;
        }

        return '项目: ' + projectKey + '，服务器: ' + serverUrl;
    }.property('projectKey', 'connection', 'connection.serverUrl'),
    name: '飞书项目配置'
});

// 飞书项目代理配置视图
App.FeishuProxyConfigurationView = Ember.View.extend({
    classNames: ["rows"],
    template: Ember.Handlebars.compile([
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>项目Key:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="projectKey" placeholder="输入飞书项目的project_key"}}',
        '    <span class="help-inline">飞书项目的唯一标识符</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">工作项类型:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="workItemTypes" placeholder="story,task,bug"}}',
        '    <span class="help-inline">要同步的工作项类型，多个类型用逗号分隔</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">排除图表类型:</label>',
        '  <div class="controls">',
        '    <label class="checkbox">',
        '      <input type="checkbox" {{bindAttr checked="excludeChartType"}} />',
        '      排除图表类型以优化通用字段计算（推荐开启）',
        '    </label>',
        '  </div>',
        '</div>',

    ].join('\n'))
});

// 飞书项目连接模型
App.FeishuConnection = App.Connection.extend({
    resourceProperties: [
        'id',
        'connectionType',
        'serverUrl',
        'authMode',
        'pluginId',  // App ID (在 plugin 模式下映射到 user 字段存储)
        'userKey',   // User Key (仅在 plugin_access_token 模式下使用)
        'password'   // App Secret 或 Token (加密存储)
    ],

    // 初始化默认值
    init: function() {
        this._super();
        if (!this.get('authMode')) {
            this.set('authMode', 'plugin_access_token');
        }
        if (!this.get('serverUrl')) {
            this.set('serverUrl', 'https://project.feishu.cn');
        }

        // 设置字段映射观察器
        this.setupFieldMapping();
    },

    // 设置字段映射逻辑
    setupFieldMapping: function() {
        var self = this;

        // pluginId 变化时同步到 user 字段
        this.addObserver('pluginId', function() {
            var pluginId = self.get('pluginId');
            if (pluginId && pluginId !== self.get('user')) {
                self.set('user', pluginId);
            }
        });

        // user 字段变化时同步到 pluginId（但要避免循环更新）
        this.addObserver('user', function() {
            var user = self.get('user');
            var pluginId = self.get('pluginId');
            // 只有当 user 不是默认值且与 pluginId 不同时才同步
            if (user && user !== 'feishu-token' && user !== pluginId) {
                self.set('pluginId', user);
            }
        });
    },

    // 重写 set 方法以确保字段映射
    set: function(key, value) {
        var result = this._super(key, value);
        var authMode = this.get('authMode') || 'plugin_access_token';

        // 在 plugin 模式下，pluginId 映射到 user 字段进行存储
        if (key === 'pluginId' && value && authMode === 'plugin_access_token') {
            this._super('user', value);
        }

        return result;
    },

    description: function() {
        var serverUrl = this.get('serverUrl') || 'https://project.feishu.cn';
        return '飞书项目连接 (插件访问凭证): ' + serverUrl;
    }.property('serverUrl')
});

// 飞书项目连接视图
App.FeishuConnectionView = Ember.View.extend({
    classNames: ['rows'],
    template: Ember.Handlebars.compile([
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>Server URL:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="controller.content.serverUrl" placeholder="https://project.feishu.cn"}}',
        '  </div>',
        '</div>',

        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>App ID:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="controller.content.pluginId" placeholder="cli_a7fbxxxxxxxxxxxx"}}',
        '    <span class="help-inline">飞书项目应用的唯一标识符 (app_id)</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>App Secret:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField type="password" valueBinding="controller.content.password" placeholder="输入应用密钥"}}',
        '    <span class="help-inline">应用密钥 (app_secret)，用于获取插件访问凭证</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">User Key:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="controller.content.userKey" placeholder="输入用户Key（可选）"}}',
        '    <span class="help-inline">用于指定接口调用的用户标识，可通过双击飞书用户头像获取</span>',
        '  </div>',
        '</div>',


    ].join('\n')),








});

// 飞书项目代理配置控制器
App.FeishuProxyConfigurationController = Ember.ObjectController.extend({
    // 可以在这里添加自定义的控制器逻辑

    // 验证项目Key格式
    validateProjectKey: function() {
        var projectKey = this.get('projectKey');
        if (projectKey && projectKey.trim().length > 0) {
            // 可以添加项目Key格式验证逻辑
            return true;
        }
        return false;
    },

    // 验证字段映射配置格式
    validateFieldMapping: function() {
        var fieldMappingConfig = this.get('fieldMappingConfig');
        if (!fieldMappingConfig || fieldMappingConfig.trim().length === 0) {
            return true; // 空配置是允许的
        }

        try {
            var mappings = fieldMappingConfig.split(',');
            for (var i = 0; i < mappings.length; i++) {
                var mapping = mappings[i].trim();
                var parts = mapping.split(':');
                if (parts.length < 2) {
                    return false;
                }
            }
            return true;
        } catch (e) {
            return false;
        }
    },

    // 获取工作项类型选项
    workItemTypeOptions: [
        { value: 'story', label: 'Story' },
        { value: 'task', label: 'Task' },
        { value: 'bug', label: 'Bug' },
        { value: 'epic', label: 'Epic' }
    ],

    // 获取字段类型选项
    fieldTypeOptions: [
        { value: 'TEXT', label: '文本' },
        { value: 'NUMBER', label: '数字' },
        { value: 'OPTION', label: '选项' },
        { value: 'USER', label: '用户' },
        { value: 'DATE', label: '日期' },
        { value: 'MULTI_OPTION', label: '多选' },
        { value: 'RICH_TEXT', label: '富文本' }
    ],

    // 字段映射管理
    fieldMappings: [],

    // 添加字段映射
    addFieldMapping: function() {
        var newMapping = Ember.Object.create({
            leftField: '',
            rightField: '',
            direction: 'BIDIRECTIONAL',
            primaryDirection: 'LEFT_TO_RIGHT',
            valueMappings: []
        });
        this.get('fieldMappings').pushObject(newMapping);
    },

    // 删除字段映射
    removeFieldMapping: function(mapping) {
        this.get('fieldMappings').removeObject(mapping);
    },

    // 为字段映射添加值映射
    addValueMapping: function(fieldMapping) {
        var newValueMapping = Ember.Object.create({
            leftValue: '',
            rightValue: ''
        });
        fieldMapping.get('valueMappings').pushObject(newValueMapping);
    },

    // 删除值映射
    removeValueMapping: function(fieldMapping, valueMapping) {
        fieldMapping.get('valueMappings').removeObject(valueMapping);
    },

    // 显示字段映射编辑器
    showFieldMapping: function() {
        // 创建字段映射编辑器模态框
        var modal = App.FeishuFieldMappingModal.create({
            controller: this
        });
        modal.show();
    },

    // 从飞书API获取字段列表
    loadFeishuFields: function() {
        var self = this;
        var projectKey = this.get('projectKey');
        var connection = this.get('connection');

        if (!projectKey || !connection) {
            return;
        }

        // 这里应该调用后端API获取飞书字段列表
        // 暂时使用示例数据
        console.log('Loading Feishu fields for project:', projectKey);
    },

    // 从Polarion获取字段列表
    loadPolarionFields: function() {
        var self = this;

        // 这里应该调用Polarion API获取字段列表
        // 暂时使用示例数据
        console.log('Loading Polarion fields');
    }
});

// 飞书项目连接控制器
App.FeishuConnectionController = Ember.ObjectController.extend({

    // 初始化
    init: function() {
        this._super();
        // 延迟初始化，确保content对象已经设置
        Ember.run.next(this, function() {
            if (this.get('content')) {
                this.set('content.authMode', 'plugin_access_token');
                // 确保字段映射设置完成
                if (this.get('content').setupFieldMapping) {
                    this.get('content').setupFieldMapping();
                }
                // 初始化字段映射
                this.initializeFieldMapping();
            }
        });
    },

    // 初始化字段映射
    initializeFieldMapping: function() {
        var content = this.get('content');
        if (content) {
            var pluginId = content.get('pluginId');
            var user = content.get('user');

            // Plugin 模式：pluginId 映射到 user 字段存储
            if (pluginId) {
                content.set('user', pluginId);
            } else if (user) {
                // 如果从存储中读取到 user 值，映射回 pluginId 显示
                content.set('pluginId', user);
            }
        }
    },









    // 观察 content 变化，确保字段映射
    contentChanged: function() {
        Ember.run.next(this, function() {
            this.initializeFieldMapping();
        });
    }.observes('content'),

    // 观察 pluginId 变化，同步到 user 字段
    pluginIdChanged: function() {
        var content = this.get('content');
        if (content) {
            var pluginId = content.get('pluginId');
            if (pluginId && pluginId !== content.get('user')) {
                content.set('user', pluginId);
            }
        }
    }.observes('content.pluginId')
});

// 字段映射编辑器视图
App.FeishuFieldMappingView = Ember.View.extend({
    templateName: 'feishuFieldMapping',
    classNames: ['field-mapping-editor'],

    // 可用的Polarion字段（示例数据，实际应该从服务器获取）
    polarionFields: [
        { id: 'title', name: '标题', type: 'string' },
        { id: 'description', name: '描述', type: 'rich-text' },
        { id: 'status', name: '状态', type: 'option' },
        { id: 'priority', name: '优先级', type: 'option' },
        { id: 'assignee', name: '指派人', type: 'user' },
        { id: 'author', name: '创建人', type: 'user' },
        { id: 'created', name: '创建时间', type: 'date-time' },
        { id: 'updated', name: '更新时间', type: 'date-time' }
    ],

    // 可用的飞书字段（示例数据，实际应该从飞书API获取）
    feishuFields: [
        { id: 'title', name: '标题', type: 'text' },
        { id: 'description', name: '描述', type: 'rich_text' },
        { id: 'status', name: '状态', type: 'select' },
        { id: 'priority', name: '优先级', type: 'select' },
        { id: 'assignee', name: '指派人', type: 'user' },
        { id: 'creator', name: '创建人', type: 'user' },
        { id: 'created_at', name: '创建时间', type: 'datetime' },
        { id: 'updated_at', name: '更新时间', type: 'datetime' }
    ],

    // 字段映射方向选项
    directionOptions: [
        { value: 'LEFT_TO_RIGHT', label: 'Polarion → 飞书' },
        { value: 'RIGHT_TO_LEFT', label: '飞书 → Polarion' },
        { value: 'BIDIRECTIONAL', label: '双向同步' }
    ],

    actions: {
        // 添加字段映射
        addFieldMapping: function() {
            var controller = this.get('controller');
            if (controller && controller.addFieldMapping) {
                controller.addFieldMapping();
            }
        },

        // 删除字段映射
        removeFieldMapping: function(mapping) {
            var controller = this.get('controller');
            if (controller && controller.removeFieldMapping) {
                controller.removeFieldMapping(mapping);
            }
        },

        // 添加值映射
        addValueMapping: function(fieldMapping) {
            var controller = this.get('controller');
            if (controller && controller.addValueMapping) {
                controller.addValueMapping(fieldMapping);
            }
        },

        // 删除值映射
        removeValueMapping: function(fieldMapping, valueMapping) {
            var controller = this.get('controller');
            if (controller && controller.removeValueMapping) {
                controller.removeValueMapping(fieldMapping, valueMapping);
            }
        },

        // 获取字段选项
        getFieldOptions: function(fieldMapping) {
            var leftField = fieldMapping.get('leftField');
            var rightField = fieldMapping.get('rightField');

            // 这里应该调用API获取字段的选项值
            // 暂时返回示例数据
            return {
                leftOptions: this.getOptionsForField(leftField, 'polarion'),
                rightOptions: this.getOptionsForField(rightField, 'feishu')
            };
        }
    },

    // 获取字段的选项值
    getOptionsForField: function(fieldId, system) {
        // 示例选项数据，实际应该从API获取
        var optionsMap = {
            'polarion': {
                'status': [
                    { id: 'open', name: '开放' },
                    { id: 'in-progress', name: '进行中' },
                    { id: 'resolved', name: '已解决' },
                    { id: 'closed', name: '已关闭' }
                ],
                'priority': [
                    { id: 'low', name: '低' },
                    { id: 'medium', name: '中' },
                    { id: 'high', name: '高' },
                    { id: 'urgent', name: '紧急' }
                ]
            },
            'feishu': {
                'status': [
                    { id: '1', name: '待处理' },
                    { id: '2', name: '处理中' },
                    { id: '3', name: '已完成' },
                    { id: '4', name: '已关闭' }
                ],
                'priority': [
                    { id: '1', name: 'P0' },
                    { id: '2', name: 'P1' },
                    { id: '3', name: 'P2' },
                    { id: '4', name: 'P3' }
                ]
            }
        };

        return optionsMap[system] && optionsMap[system][fieldId] || [];
    }
});

// 字段映射模态框
App.FeishuFieldMappingModal = Ember.View.extend({
    templateName: 'feishuFieldMappingModal',
    classNames: ['modal', 'fade'],
    attributeBindings: ['tabindex'],
    tabindex: -1,

    didInsertElement: function() {
        this._super();
        var self = this;
        this.$().modal({
            backdrop: 'static',
            keyboard: false
        });

        // 模态框关闭时销毁视图
        this.$().on('hidden.bs.modal', function() {
            self.destroy();
        });
    },

    show: function() {
        this.$().modal('show');
    },

    hide: function() {
        this.$().modal('hide');
    },

    actions: {
        save: function() {
            // 保存字段映射配置
            var controller = this.get('controller');
            if (controller && controller.saveFieldMappings) {
                controller.saveFieldMappings();
            }
            this.hide();
        },

        cancel: function() {
            this.hide();
        }
    }
});
