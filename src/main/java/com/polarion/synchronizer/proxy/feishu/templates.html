<!-- 飞书项目代理配置模板 -->
<script type="text/x-handlebars" data-template-name="feishuProxyConfiguration">
  <div class="control-group">
    <label class="control-label"><span style="color:red">*</span>项目Key:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="projectKey" placeholder="输入飞书项目的project_key"}}
      <span class="help-inline">飞书项目的唯一标识符</span>
    </div>
  </div>
  
  <div class="control-group">
    <label class="control-label">工作项类型:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="workItemTypes" placeholder="story,task,bug"}}
      <span class="help-inline">要同步的工作项类型，多个类型用逗号分隔</span>
    </div>
  </div>

  <div class="control-group">
    <label class="control-label">排除图表类型:</label>
    <div class="controls">
      <label class="checkbox">
        <input type="checkbox" {{bindAttr checked="excludeChartType"}} />
        排除图表类型以优化通用字段计算（推荐开启）
      </label>
    </div>
  </div>

  <!-- 字段映射配置区域 -->
  <div class="control-group">
    <label class="control-label">字段映射:</label>
    <div class="controls">
      <button type="button" class="btn btn-info" {{action "showFieldMapping" target="controller"}}>
        <i class="fa fa-cog"></i> 配置字段映射
      </button>
      <span class="help-inline">点击配置Polarion与飞书项目之间的字段映射关系</span>
    </div>
  </div>

</script>

<!-- 飞书项目连接配置模板 -->
<script type="text/x-handlebars" data-template-name="feishuConnection" style="display:table-row-group">
  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>Server URL:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.serverUrl"
             disabledBinding="controller.readOnly"
             placeholder="https://project.feishu.cn"}}
      <span class="help-inline">飞书项目服务器地址</span>
    </div>
  </div>

  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>访问凭证类型:</label>
    <div class="controls">
      <select {{bindAttr value="controller.content.authMode"}}>
        <option value="plugin_access_token">插件访问凭证 (App ID + App Secret)</option>
        <option value="virtual_plugin_token">虚拟插件访问凭证 (开发调试用)</option>
      </select>
      <span class="help-inline">选择飞书项目访问凭证类型</span>
    </div>
  </div>

  {{#if controller.isPluginAccessToken}}
  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>App ID:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.pluginId"
             placeholder="cli_a7fbxxxxxxxxxxxx"}}
      <span class="help-inline">飞书项目应用的唯一标识符 (app_id)</span>
    </div>
  </div>

  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>App Secret:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.password"
             type="password"
             placeholder="输入应用密钥"}}
      <span class="help-inline">应用密钥 (app_secret)，用于获取插件访问凭证</span>
    </div>
  </div>
  {{/if}}

  {{#if controller.isVirtualPluginToken}}
  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>Virtual Plugin Token:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.password"
             type="password"
             placeholder="v-b0dc4a7d-9080-4bc3-b54a-76b68056****"}}
      <span class="help-inline">虚拟插件访问凭证，仅供开发调试使用</span>
    </div>
  </div>
  {{/if}}

  {{#if controller.isUserAccessToken}}
  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>User Access Token:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.password"
             type="password"
             placeholder="u-f09b09b8-e78a-405b-9733-7261787e****"}}
      <span class="help-inline">用户访问凭证，代表用户对插件的临时授权</span>
    </div>
  </div>
  {{/if}}

  <div class="control-group">
    <div class="controls">
      <button type="button" class="btn btn-primary" {{action "testConnection" target="controller"}}>
        测试连接
      </button>
      <span class="help-inline">点击测试与飞书项目的连接是否正常</span>
    </div>
  </div>
</script>

<!-- 字段映射编辑器模板 -->
<script type="text/x-handlebars" data-template-name="feishuFieldMapping">
  <div class="field-mapping-container">
    <h4>字段映射配置</h4>
    <p class="help-block">配置Polarion与飞书项目之间的字段映射关系</p>

    <!-- 字段映射列表 -->
    <div class="field-mappings">
      {{#each fieldMapping in controller.fieldMappings}}
        <div class="field-mapping-item panel panel-default">
          <div class="panel-heading">
            <div class="row">
              <div class="col-md-4">
                <label>Polarion字段:</label>
                {{view Ember.Select
                  valueBinding="fieldMapping.leftField"
                  contentBinding="view.polarionFields"
                  optionValuePath="content.id"
                  optionLabelPath="content.name"
                  prompt="选择Polarion字段"
                  class="form-control"}}
              </div>
              <div class="col-md-1 text-center">
                <i class="fa fa-arrow-right" style="margin-top: 25px;"></i>
              </div>
              <div class="col-md-4">
                <label>飞书字段:</label>
                {{view Ember.Select
                  valueBinding="fieldMapping.rightField"
                  contentBinding="view.feishuFields"
                  optionValuePath="content.id"
                  optionLabelPath="content.name"
                  prompt="选择飞书字段"
                  class="form-control"}}
              </div>
              <div class="col-md-2">
                <label>同步方向:</label>
                {{view Ember.Select
                  valueBinding="fieldMapping.direction"
                  contentBinding="view.directionOptions"
                  optionValuePath="content.value"
                  optionLabelPath="content.label"
                  class="form-control"}}
              </div>
              <div class="col-md-1">
                <button type="button" class="btn btn-danger btn-sm"
                        {{action "removeFieldMapping" fieldMapping target="view"}}
                        style="margin-top: 25px;">
                  <i class="fa fa-trash"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- 值映射配置 -->
          {{#if fieldMapping.leftField}}
            {{#if fieldMapping.rightField}}
              <div class="panel-body">
                <h5>选项值映射
                  <small class="text-muted">(仅适用于选项类型字段)</small>
                  <button type="button" class="btn btn-primary btn-xs pull-right"
                          {{action "addValueMapping" fieldMapping target="view"}}>
                    <i class="fa fa-plus"></i> 添加映射
                  </button>
                </h5>

                {{#if fieldMapping.valueMappings.length}}
                  <table class="table table-condensed">
                    <thead>
                      <tr>
                        <th>Polarion选项</th>
                        <th></th>
                        <th>飞书选项</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {{#each valueMapping in fieldMapping.valueMappings}}
                        <tr>
                          <td>
                            {{view Ember.TextField
                              valueBinding="valueMapping.leftValue"
                              placeholder="输入Polarion选项值或名称"
                              class="form-control input-sm"}}
                          </td>
                          <td class="text-center">
                            <i class="fa fa-arrow-right"></i>
                          </td>
                          <td>
                            {{view Ember.TextField
                              valueBinding="valueMapping.rightValue"
                              placeholder="输入飞书选项ID或名称"
                              class="form-control input-sm"}}
                          </td>
                          <td>
                            <button type="button" class="btn btn-danger btn-xs"
                                    {{action "removeValueMapping" fieldMapping valueMapping target="view"}}>
                              <i class="fa fa-trash"></i>
                            </button>
                          </td>
                        </tr>
                      {{/each}}
                    </tbody>
                  </table>
                {{else}}
                  <p class="text-muted">暂无选项值映射</p>
                {{/if}}
              </div>
            {{/if}}
          {{/if}}
        </div>
      {{/each}}
    </div>

    <!-- 添加字段映射按钮 -->
    <div class="text-center" style="margin-top: 20px;">
      <button type="button" class="btn btn-success" {{action "addFieldMapping" target="view"}}>
        <i class="fa fa-plus"></i> 添加字段映射
      </button>
    </div>

    <!-- 帮助信息 -->
    <div class="alert alert-info" style="margin-top: 20px;">
      <h5><i class="fa fa-info-circle"></i> 使用说明:</h5>
      <ul>
        <li><strong>字段映射</strong>: 选择要同步的Polarion和飞书字段</li>
        <li><strong>同步方向</strong>: 选择数据同步的方向</li>
        <li><strong>选项值映射</strong>: 对于选项类型字段，可以配置选项值之间的对应关系</li>
        <li><strong>支持名称映射</strong>: 在选项值映射中，可以使用选项名称而不是ID</li>
      </ul>
    </div>
  </div>
</script>

<!-- 字段映射模态框模板 -->
<script type="text/x-handlebars" data-template-name="feishuFieldMappingModal">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" {{action "cancel" target="view"}}>
          <span>&times;</span>
        </button>
        <h4 class="modal-title">
          <i class="fa fa-cog"></i> 字段映射配置
        </h4>
      </div>

      <div class="modal-body">
        {{view App.FeishuFieldMappingView controllerBinding="view.controller"}}
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-default" {{action "cancel" target="view"}}>
          取消
        </button>
        <button type="button" class="btn btn-primary" {{action "save" target="view"}}>
          <i class="fa fa-save"></i> 保存配置
        </button>
      </div>
    </div>
  </div>
</script>
