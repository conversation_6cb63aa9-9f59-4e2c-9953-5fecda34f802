package com.polarion.synchronizer.proxy.feishu;

import com.google.inject.AbstractModule;
import com.polarion.synchronizer.ProxyContribution;
import com.polarion.synchronizer.spi.ProxyBinder;
import com.polarion.synchronizer.spi.translators.TranslatorModule;
import com.polarion.synchronizer.proxy.feishu.translators.PolarionToFeishuUserTranslator;
import com.polarion.synchronizer.proxy.feishu.translators.FeishuToPolarionUserTranslator;

/**
 * 飞书项目连接器的Guice模块
 * 负责注册飞书项目连接器到Polarion同步器框架
 */
public class GuiceModule extends AbstractModule {
    
    @Override
    protected void configure() {
        // 注册飞书项目连接器
        String pathToUi = "feishu.js";
        ProxyBinder.bindProxy(
            this.binder(),
            new ProxyContribution("Feishu", FeishuProxyConfiguration.class, FeishuConnection.class, pathToUi),
            FeishuProxyFactory.class
        );
        
        // 注册用户字段转换器
        // Polarion用户字段 -> 飞书用户字段
        this.install(new TranslatorModule<String>("user", "feishu:user", PolarionToFeishuUserTranslator.class) {
        });

        // 飞书用户字段 -> Polarion用户字段
        this.install(new TranslatorModule<String>("feishu:user", "user", FeishuToPolarionUserTranslator.class) {
        });

        // 可以在这里添加其他的转换器，例如：
        // - 富文本转换器
        // - 日期时间转换器
        // - 枚举值转换器（通过XML配置的replace元素处理）
    }
}
