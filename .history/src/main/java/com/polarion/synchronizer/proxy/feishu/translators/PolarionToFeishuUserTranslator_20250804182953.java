package com.polarion.synchronizer.proxy.feishu.translators;

import com.google.inject.assistedinject.Assisted;
import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;
import com.lark.project.service.user.model.UserBasicInfo;
import com.polarion.alm.tracker.ITrackerService;
import com.polarion.platform.core.PlatformContext;
import com.polarion.platform.security.AuthenticationFailedException;
import com.polarion.platform.security.auth.UserAuthenticationProvider;
import com.polarion.synchronizer.ILogger;
import com.polarion.synchronizer.mapping.TranslationResult;
import com.polarion.synchronizer.mapping.ValueMapping;
import com.polarion.synchronizer.model.Side;
import com.polarion.synchronizer.proxy.feishu.FeishuConnection;
import com.polarion.synchronizer.proxy.feishu.FeishuProxyConfiguration;
import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.inject.Inject;
import java.util.Collection;
import java.util.Collections;

/**
 * Polarion用户字段到飞书用户字段的转换器
 * 处理用户ID的双向映射和转换
 *
 * 实现功能：
 * 1. Polarion -> 飞书：通过邮箱查询飞书用户，获取飞书用户Key
 * 2. 飞书 -> Polarion：通过飞书用户Key获取邮箱，然后在Polarion中查找对应用户
 *
 * API调用说明：
 * - 使用飞书项目API的 /open_api/user/query 接口
 * - 支持通过邮箱地址精确查询用户信息
 * - 返回用户的userKey、email、username等详细信息
 *
 * 异常处理：
 * - API调用失败时记录警告日志并返回原值
 * - 网络异常或配置错误时降级处理
 * - 支持值映射配置作为备选方案
 *
 * 权限要求：
 * - 需要飞书应用具有用户信息读取权限
 * - 需要正确配置App ID和App Secret
 * - 可选配置User Key用于指定调用用户身份
 */
public class PolarionToFeishuUserTranslator extends TypesafeTranslator<String, String, String> {

    @NotNull
    private final UserAuthenticationProvider userAuthProvider;

    @NotNull
    private final ITrackerService trackerService;

    @NotNull
    private final ILogger logger;

    @NotNull
    private final Collection<ValueMapping> valueMappings;

    @NotNull
    private final Side fromSide;

    @NotNull
    private final FeishuProxyConfiguration configuration;

    @Inject
    public PolarionToFeishuUserTranslator(@Assisted Collection<ValueMapping> valueMappings,
                                         @Assisted Side fromSide,
                                         @Assisted FeishuProxyConfiguration configuration,
                                         @NotNull ILogger logger) {
        super(String.class, String.class);
        this.userAuthProvider = PlatformContext.getPlatform().lookupService(UserAuthenticationProvider.class);
        this.trackerService = PlatformContext.getPlatform().lookupService(ITrackerService.class);
        this.valueMappings = valueMappings;
        this.fromSide = fromSide;
        this.configuration = configuration;
        this.logger = logger;
    }

    @Override
    public TranslationResult<String> translateUnidirectionalTypesafe(@Nullable String sourceValue, 
                                                                   @Nullable String targetValue) {
        String mappedValue = convertUser(sourceValue);
        return createUnidirectionalResult(mappedValue, targetValue);
    }

    @Override
    public TranslationResult<String> translateBidirectionalTypesafe(@Nullable String sourceBaseline, 
                                                                  @Nullable String sourceValue, 
                                                                  @Nullable String targetBaseline, 
                                                                  @Nullable String targetValue) {
        String mappedValue = convertUser(sourceValue);
        return createBidirectionalResult(sourceBaseline, sourceValue, mappedValue, targetBaseline, targetValue);
    }

    /**
     * 转换用户字段值
     */
    @Nullable
    private String convertUser(@Nullable String userValue) {
        if (userValue == null || userValue.trim().isEmpty()) {
            return null;
        }

        try {
            // 首先检查是否有直接的值映射配置
            String mappedValue = applyValueMappings(userValue);
            if (mappedValue != null && !mappedValue.equals(userValue)) {
                logger.debug("通过值映射转换用户: " + userValue + " -> " + mappedValue);
                return mappedValue;
            }

            // 根据同步方向进行转换
            if (fromSide == Side.LEFT) {
                // Polarion -> 飞书
                return convertPolarionToFeishuUser(userValue);
            } else {
                // 飞书 -> Polarion
                return convertFeishuToPolarionUser(userValue);
            }
            
        } catch (Exception e) {
            logger.warn("用户字段转换失败: " + userValue, e);
            return userValue; // 转换失败时返回原值
        }
    }

    /**
     * 应用值映射配置
     */
    @Nullable
    private String applyValueMappings(@NotNull String value) {
        for (ValueMapping mapping : valueMappings) {
            if (fromSide == Side.LEFT && value.equals(mapping.getLeft())) {
                return mapping.getRight();
            } else if (fromSide == Side.RIGHT && value.equals(mapping.getRight())) {
                return mapping.getLeft();
            }
        }
        return null;
    }

    /**
     * 将Polarion用户ID转换为飞书用户ID
     */
    @Nullable
    private String convertPolarionToFeishuUser(@NotNull String polarionUserId) {
        try {
            // 移除可能的前缀
            String cleanUserId = polarionUserId.startsWith("user:") ?
                polarionUserId.substring(5) : polarionUserId;

            // 尝试通过UserAuthenticationProvider获取规范化的用户名
            String canonicalUserName = userAuthProvider.getCandidateOrOriginalUserName(cleanUserId);

            // 获取Polarion用户的邮箱地址
            String userEmail = getPolarionUserEmail(canonicalUserName);
            if (userEmail != null) {
                // 通过邮箱查找飞书用户
                String feishuUserKey = queryFeishuUserByEmail(userEmail);
                if (feishuUserKey != null) {
                    logger.debug("通过邮箱查询成功，Polarion用户转换: " + polarionUserId + " -> " + feishuUserKey);
                    return feishuUserKey;
                }
            }

            // 如果邮箱查询失败，尝试直接使用用户名查询
            String feishuUserKey = queryFeishuUserByEmail(canonicalUserName);
            if (feishuUserKey != null) {
                logger.debug("通过用户名查询成功，Polarion用户转换: " + polarionUserId + " -> " + feishuUserKey);
                return feishuUserKey;
            }

            // 如果都失败，返回规范化的用户名
            logger.debug("飞书用户查询失败，使用规范化用户名: " + polarionUserId + " -> " + canonicalUserName);
            return canonicalUserName;

        } catch (AuthenticationFailedException e) {
            logger.warn("无法找到Polarion用户: " + polarionUserId, e);
            return polarionUserId;
        }
    }

    /**
     * 将飞书用户ID转换为Polarion用户ID
     */
    @Nullable
    private String convertFeishuToPolarionUser(@NotNull String feishuUserId) {
        try {
            // 通过飞书用户Key查询用户详情，获取邮箱
            String userEmail = queryFeishuUserEmail(feishuUserId);
            if (userEmail != null) {
                // 通过邮箱在Polarion中查找对应用户
                String polarionUserId = findPolarionUserByEmail(userEmail);
                if (polarionUserId != null) {
                    logger.debug("通过邮箱查询成功，飞书用户转换: " + feishuUserId + " -> " + polarionUserId);
                    return polarionUserId;
                }
            }

            // 如果查询失败，返回原值
            logger.debug("飞书用户转换失败，返回原值: " + feishuUserId);
            return feishuUserId;

        } catch (Exception e) {
            logger.warn("飞书用户转换失败: " + feishuUserId, e);
            return feishuUserId;
        }
    }

    /**
     * 通过邮箱查询飞书用户信息
     * @param email 邮箱地址
     * @return 飞书用户Key，如果未找到则返回null
     */
    @Nullable
    private String queryFeishuUserByEmail(@NotNull String email) {
        try {
            // 获取飞书API客户端
            Client feishuClient = getFeishuClient();
            if (feishuClient == null) {
                logger.warn("无法获取飞书API客户端");
                return null;
            }

            // 构建查询请求
            QueryUserDetailReq req = QueryUserDetailReq.newBuilder()
                .emails(Collections.singletonList(email))
                .build();

            // 创建请求选项
            RequestOptions options = createRequestOptions();

            // 调用API查询用户详情
            QueryUserDetailResp resp = feishuClient.getUserService().queryUserDetail(req, options);

            if (resp.success() && resp.getData() != null && !resp.getData().isEmpty()) {
                UserBasicInfo userInfo = resp.getData().get(0);
                String userKey = userInfo.getUserKey();
                logger.debug("通过邮箱 " + email + " 找到飞书用户: " + userKey);
                return userKey;
            } else {
                logger.debug("通过邮箱 " + email + " 未找到飞书用户");
                return null;
            }

        } catch (Exception e) {
            logger.warn("查询飞书用户失败，邮箱: " + email, e);
            return null;
        }
    }

    /**
     * 通过飞书用户Key查询用户邮箱
     * @param userKey 飞书用户Key
     * @return 用户邮箱，如果未找到则返回null
     */
    @Nullable
    private String queryFeishuUserEmail(@NotNull String userKey) {
        try {
            // 构建查询请求
            QueryUserDetailReq req = QueryUserDetailReq.newBuilder()
                .userKeys(Collections.singletonList(userKey))
                .build();

            // 创建请求选项
            RequestOptions options = createRequestOptions();

            // 调用API查询用户详情
            QueryUserDetailResp resp = feishuClient.getUserService().queryUserDetail(req, options);

            if (resp.success() && resp.getData() != null && !resp.getData().isEmpty()) {
                UserBasicInfo userInfo = resp.getData().get(0);
                String email = userInfo.getEmail();
                logger.debug("飞书用户 " + userKey + " 的邮箱: " + email);
                return email;
            } else {
                logger.debug("未找到飞书用户: " + userKey);
                return null;
            }

        } catch (Exception e) {
            logger.warn("查询飞书用户邮箱失败，用户Key: " + userKey, e);
            return null;
        }
    }

    /**
     * 获取Polarion用户的邮箱地址
     * @param userId Polarion用户ID
     * @return 用户邮箱，如果未找到则返回null
     */
    @Nullable
    private String getPolarionUserEmail(@NotNull String userId) {
        try {
            // 尝试通过TrackerService获取用户信息
            if (trackerService != null) {
                // 注意：这里假设用户ID就是邮箱格式，或者可以通过其他方式获取邮箱
                // 实际实现可能需要根据具体的Polarion配置和用户管理方式调整

                // 如果用户ID本身就是邮箱格式，直接返回
                if (userId.contains("@")) {
                    logger.debug("用户ID本身是邮箱格式: " + userId);
                    return userId;
                }

                // 尝试构造邮箱地址（这里需要根据实际的邮箱规则调整）
                // 例如：用户名@公司域名.com
                // 这里只是示例，实际使用时需要根据具体情况修改
                logger.debug("无法直接获取用户邮箱，用户ID: " + userId);
                return null;
            }

            return null;

        } catch (Exception e) {
            logger.warn("获取Polarion用户邮箱失败，用户ID: " + userId, e);
            return null;
        }
    }

    /**
     * 通过邮箱在Polarion中查找用户
     * @param email 邮箱地址
     * @return Polarion用户ID，如果未找到则返回null
     */
    @Nullable
    private String findPolarionUserByEmail(@NotNull String email) {
        try {
            // 在Polarion中通过邮箱查找用户
            // 注意：这里使用邮箱作为用户名进行查找，实际实现可能需要根据具体的Polarion配置调整
            String userId = userAuthProvider.getCandidateOrOriginalUserName(email);
            if (userId != null && !userId.equals(email)) {
                logger.debug("通过邮箱 " + email + " 找到Polarion用户: " + userId);
                return userId;
            }

            // 如果直接查找失败，可以尝试其他方式
            // 例如通过TrackerService查询用户
            logger.debug("通过邮箱 " + email + " 未找到Polarion用户");
            return null;

        } catch (Exception e) {
            logger.warn("在Polarion中查找用户失败，邮箱: " + email, e);
            return null;
        }
    }



    /**
     * 创建请求选项
     * @return 请求选项
     */
    @NotNull
    private RequestOptions createRequestOptions() {
        FeishuConnection connection = (FeishuConnection) configuration.getConnection();
        RequestOptions.Builder builder = RequestOptions.newBuilder();

        if (connection != null) {
            String userKey = connection.getUserKey();
            if (userKey != null && !userKey.isEmpty()) {
                builder.userKey(userKey);
            }
        }

        return builder.build();
    }
}
