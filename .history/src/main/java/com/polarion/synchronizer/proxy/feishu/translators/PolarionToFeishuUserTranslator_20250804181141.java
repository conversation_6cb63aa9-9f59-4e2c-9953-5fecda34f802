package com.polarion.synchronizer.proxy.feishu.translators;

import com.google.inject.assistedinject.Assisted;
import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;
import com.lark.project.service.user.model.UserBasicInfo;
import com.polarion.alm.tracker.ITrackerService;
import com.polarion.platform.core.PlatformContext;
import com.polarion.platform.i18n.Localization;
import com.polarion.platform.security.AuthenticationFailedException;
import com.polarion.platform.security.auth.UserAuthenticationProvider;
import com.polarion.synchronizer.ILogger;
import com.polarion.synchronizer.SynchronizationException;
import com.polarion.synchronizer.mapping.TranslationResult;
import com.polarion.synchronizer.mapping.ValueMapping;
import com.polarion.synchronizer.model.Side;
import com.polarion.synchronizer.proxy.feishu.FeishuConnection;
import com.polarion.synchronizer.proxy.feishu.FeishuProxyConfiguration;
import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.inject.Inject;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Polarion用户字段到飞书用户字段的转换器
 * 处理用户ID的双向映射和转换
 */
public class PolarionToFeishuUserTranslator extends TypesafeTranslator<String, String, String> {

    @NotNull
    private final UserAuthenticationProvider userAuthProvider;

    @NotNull
    private final ITrackerService trackerService;

    @NotNull
    private final ILogger logger;

    @NotNull
    private final Collection<ValueMapping> valueMappings;

    @NotNull
    private final Side fromSide;

    @NotNull
    private final FeishuProxyConfiguration configuration;

    @Inject
    public PolarionToFeishuUserTranslator(@Assisted Collection<ValueMapping> valueMappings,
                                         @Assisted Side fromSide,
                                         @Assisted FeishuProxyConfiguration configuration,
                                         @NotNull ILogger logger) {
        super(String.class, String.class);
        this.userAuthProvider = PlatformContext.getPlatform().lookupService(UserAuthenticationProvider.class);
        this.trackerService = PlatformContext.getPlatform().lookupService(ITrackerService.class);
        this.valueMappings = valueMappings;
        this.fromSide = fromSide;
        this.configuration = configuration;
        this.logger = logger;
    }

    @Override
    public TranslationResult<String> translateUnidirectionalTypesafe(@Nullable String sourceValue, 
                                                                   @Nullable String targetValue) {
        String mappedValue = convertUser(sourceValue);
        return createUnidirectionalResult(mappedValue, targetValue);
    }

    @Override
    public TranslationResult<String> translateBidirectionalTypesafe(@Nullable String sourceBaseline, 
                                                                  @Nullable String sourceValue, 
                                                                  @Nullable String targetBaseline, 
                                                                  @Nullable String targetValue) {
        String mappedValue = convertUser(sourceValue);
        return createBidirectionalResult(sourceBaseline, sourceValue, mappedValue, targetBaseline, targetValue);
    }

    /**
     * 转换用户字段值
     */
    @Nullable
    private String convertUser(@Nullable String userValue) {
        if (userValue == null || userValue.trim().isEmpty()) {
            return null;
        }

        try {
            // 首先检查是否有直接的值映射配置
            String mappedValue = applyValueMappings(userValue);
            if (mappedValue != null && !mappedValue.equals(userValue)) {
                logger.debug("通过值映射转换用户: " + userValue + " -> " + mappedValue);
                return mappedValue;
            }

            // 根据同步方向进行转换
            if (fromSide == Side.LEFT) {
                // Polarion -> 飞书
                return convertPolarionToFeishuUser(userValue);
            } else {
                // 飞书 -> Polarion
                return convertFeishuToPolarionUser(userValue);
            }
            
        } catch (Exception e) {
            logger.warn("用户字段转换失败: " + userValue, e);
            return userValue; // 转换失败时返回原值
        }
    }

    /**
     * 应用值映射配置
     */
    @Nullable
    private String applyValueMappings(@NotNull String value) {
        for (ValueMapping mapping : valueMappings) {
            if (fromSide == Side.LEFT && value.equals(mapping.getLeft())) {
                return mapping.getRight();
            } else if (fromSide == Side.RIGHT && value.equals(mapping.getRight())) {
                return mapping.getLeft();
            }
        }
        return null;
    }

    /**
     * 将Polarion用户ID转换为飞书用户ID
     */
    @Nullable
    private String convertPolarionToFeishuUser(@NotNull String polarionUserId) {
        try {
            // 移除可能的前缀
            String cleanUserId = polarionUserId.startsWith("user:") ? 
                polarionUserId.substring(5) : polarionUserId;

            // 尝试通过UserAuthenticationProvider获取规范化的用户名
            String canonicalUserName = userAuthProvider.getCandidateOrOriginalUserName(cleanUserId);
            
            // TODO: 这里可以实现具体的用户映射逻辑
            // 例如：通过邮箱查找飞书用户、通过用户名映射表等
            
            logger.debug("Polarion用户转换: " + polarionUserId + " -> " + canonicalUserName);
            return canonicalUserName;
            
        } catch (AuthenticationFailedException e) {
            logger.warn("无法找到Polarion用户: " + polarionUserId, e);
            return polarionUserId;
        }
    }

    /**
     * 将飞书用户ID转换为Polarion用户ID
     */
    @Nullable
    private String convertFeishuToPolarionUser(@NotNull String feishuUserId) {
        try {
            // TODO: 这里可以实现具体的用户映射逻辑
            // 例如：通过飞书用户API查找对应的Polarion用户
            
            logger.debug("飞书用户转换: " + feishuUserId + " -> " + feishuUserId);
            return feishuUserId;
            
        } catch (Exception e) {
            logger.warn("飞书用户转换失败: " + feishuUserId, e);
            return feishuUserId;
        }
    }
}
